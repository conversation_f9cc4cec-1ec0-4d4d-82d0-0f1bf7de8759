#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速运行双地址地理编码的脚本
根据你的需求：第2+3+5列组成地址1，第6列为地址2
"""

import subprocess
import sys
import os

def main():
    # 检查文件是否存在
    input_file = "全部縣市加油站位置.xlsx"
    if not os.path.exists(input_file):
        print(f"错误：找不到文件 {input_file}")
        return
    
    # 构建命令
    cmd = [
        sys.executable,  # Python 解释器路径
        "geocode_google.py",
        "--input", input_file,
        "--addr1-cols", "2,3,5",  # 第2+3+5列组成地址1
        "--addr2-col", "6",       # 第6列为地址2
        "--join-sep", "",         # 地址1各部分无分隔符连接
        "--limit", "3",           # 先测试前3行
        "--qps", "2"              # 降低请求频率避免超限
    ]
    
    print("运行命令：")
    print(" ".join(cmd))
    print("\n开始处理...")
    
    try:
        # 运行命令
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        # 输出结果
        if result.stdout:
            print("标准输出：")
            print(result.stdout)
        
        if result.stderr:
            print("错误输出：")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n✅ 处理完成！")
            print("输出文件：全部縣市加油站位置_dual_geocoded.xlsx")
        else:
            print(f"\n❌ 处理失败，返回码：{result.returncode}")
            
    except Exception as e:
        print(f"运行出错：{e}")

if __name__ == "__main__":
    main()
