#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量用 Google Geocoding API 将地址转换为经纬度。
- 支持 CSV（标准库）
- 支持 Excel .xlsx（若系统已安装 openpyxl）
- 去重缓存、QPS 节流、指数退避重试

使用示例：
  设置环境变量：  set GOOGLE_MAPS_API_KEY=你的key
  先小样本：     python geocode_google.py --input 全部縣市加油站位置.xlsx --address-col 地址 --limit 5
  全量：         python geocode_google.py --input 全部縣市加油站位置.xlsx --address-col 地址
  若无 openpyxl：将 Excel 导出为 CSV 后：
                 python geocode_google.py --input 全部縣市加油站位置.csv --address-col 地址

输出会新增列：lat, lng, formatted_address, partial_match, place_id, geocode_status

新增功能：
- 可按列序号拼接两组地址（例如：第2+3+5列作为地址1，第6列作为地址2），分别地理编码并计算两点距离
- 命令行参数： --addr1-cols "2,3,5" --addr2-col 6 [--join-sep ""]
"""

import os
import csv
import time
import json
import math
import argparse
import urllib.parse
import urllib.request
from typing import Dict, List, Iterable, Tuple, Optional

GOOGLE_API_URL = "https://maps.googleapis.com/maps/api/geocode/json"

try:
    import openpyxl  # type: ignore
    HAS_OPENPYXL = True
except Exception:
    HAS_OPENPYXL = False


def geocode(address: str, api_key: str, country: str = "TW", language: str = "zh-TW", region: str = "tw") -> Dict:
    params = {
        "address": address,
        "key": api_key,
        "language": language,
    }
    if country:
        params["components"] = f"country:{country}"
    if region:
        params["region"] = region
    url = f"{GOOGLE_API_URL}?{urllib.parse.urlencode(params)}"
    with urllib.request.urlopen(url, timeout=20) as resp:
        data = resp.read().decode("utf-8")
        return json.loads(data)


def normalize_addr(s: Optional[str]) -> str:
    if not s:
        return ""
    return s.strip()


def haversine_distance(lat1: float, lng1: float, lat2: float, lng2: float) -> float:
    """计算两点间的球面距离（公里）"""
    R = 6371.0  # 地球半径（公里）
    lat1_rad = math.radians(lat1)
    lng1_rad = math.radians(lng1)
    lat2_rad = math.radians(lat2)
    lng2_rad = math.radians(lng2)

    dlat = lat2_rad - lat1_rad
    dlng = lng2_rad - lng1_rad

    a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlng/2)**2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))

    return R * c


def ensure_extra_cols(fieldnames: List[str], dual_mode: bool = False) -> List[str]:
    if dual_mode:
        extra_cols = [
            "addr1", "addr2",
            "addr1_lat", "addr1_lng", "addr1_formatted", "addr1_status",
            "addr2_lat", "addr2_lng", "addr2_formatted", "addr2_status",
            "distance_km"
        ]
    else:
        extra_cols = [
            "lat", "lng", "formatted_address", "partial_match", "place_id", "geocode_status",
        ]
    for c in extra_cols:
        if c not in fieldnames:
            fieldnames.append(c)
    return fieldnames


def process_rows_dual(
    rows: Iterable[Dict[str, str]],
    fieldnames: List[str],
    addr1_cols: List[int],
    addr2_col: int,
    join_sep: str,
    api_key: str,
    country: str,
    language: str,
    region: str,
    qps: float,
    limit: Optional[int],
    cache_path: str,
) -> Iterable[Dict[str, str]]:
    """双地址模式：按列序号拼接两组地址，分别地理编码并计算距离"""
    # 载入缓存
    cache: Dict[str, Dict] = {}
    if os.path.exists(cache_path):
        try:
            with open(cache_path, "r", encoding="utf-8") as f:
                cache = json.load(f)
        except Exception:
            cache = {}

    last_call_ts = 0.0
    min_interval = 1.0 / max(qps, 0.1)

    cnt = 0
    for row in rows:
        cnt += 1
        if limit and cnt > limit:
            break

        # 将字典转为列表以便按索引访问
        row_values = list(row.values())

        # 拼接地址1（第2+3+5列 -> 索引1+2+4）
        addr1_parts = []
        for col_idx in addr1_cols:
            if 0 <= col_idx < len(row_values):
                val = normalize_addr(row_values[col_idx])
                if val:
                    addr1_parts.append(val)
        addr1 = join_sep.join(addr1_parts)

        # 拼接地址2（第6列 -> 索引5）
        addr2 = ""
        if 0 <= addr2_col < len(row_values):
            addr2 = normalize_addr(row_values[addr2_col])

        # 地理编码地址1
        addr1_result = geocode_single_address(addr1, api_key, country, language, region, cache, last_call_ts, min_interval)
        last_call_ts = addr1_result["last_call_ts"]

        # 地理编码地址2
        addr2_result = geocode_single_address(addr2, api_key, country, language, region, cache, last_call_ts, min_interval)
        last_call_ts = addr2_result["last_call_ts"]

        # 计算距离
        distance_km = ""
        if (addr1_result["lat"] and addr1_result["lng"] and
            addr2_result["lat"] and addr2_result["lng"]):
            try:
                dist = haversine_distance(
                    float(addr1_result["lat"]), float(addr1_result["lng"]),
                    float(addr2_result["lat"]), float(addr2_result["lng"])
                )
                distance_km = f"{dist:.3f}"
            except (ValueError, TypeError):
                distance_km = ""

        row.update({
            "addr1": addr1,
            "addr2": addr2,
            "addr1_lat": addr1_result["lat"],
            "addr1_lng": addr1_result["lng"],
            "addr1_formatted": addr1_result["formatted"],
            "addr1_status": addr1_result["status"],
            "addr2_lat": addr2_result["lat"],
            "addr2_lng": addr2_result["lng"],
            "addr2_formatted": addr2_result["formatted"],
            "addr2_status": addr2_result["status"],
            "distance_km": distance_km,
        })
        yield row

        # 周期性写入缓存
        if cnt % 50 == 0:
            try:
                with open(cache_path, "w", encoding="utf-8") as f:
                    json.dump(cache, f, ensure_ascii=False)
            except Exception:
                pass

    # 结束保存缓存
    try:
        with open(cache_path, "w", encoding="utf-8") as f:
            json.dump(cache, f, ensure_ascii=False)
    except Exception:
        pass


def geocode_single_address(address: str, api_key: str, country: str, language: str, region: str,
                          cache: Dict[str, Dict], last_call_ts: float, min_interval: float) -> Dict:
    """地理编码单个地址，返回结果字典"""
    if not address:
        return {
            "lat": "", "lng": "", "formatted": "", "status": "NO_ADDRESS",
            "last_call_ts": last_call_ts
        }

    if address in cache:
        res = cache[address]
        return {
            "lat": extract_lat_from_result(res),
            "lng": extract_lng_from_result(res),
            "formatted": extract_formatted_from_result(res),
            "status": res.get("status", ""),
            "last_call_ts": last_call_ts
        }

    # 节流
    now = time.time()
    wait = last_call_ts + min_interval - now
    if wait > 0:
        time.sleep(wait)

    # 重试（指数退避）
    attempt, max_attempts, backoff = 0, 5, 1.5
    while True:
        attempt += 1
        try:
            resp = geocode(address, api_key, country, language, region)
            status = resp.get("status", "")
            if status in ("OK", "ZERO_RESULTS"):
                res = resp
                cache[address] = res
                last_call_ts = time.time()
                break
            elif status in ("OVER_QUERY_LIMIT",):
                sleep_s = min(60, (backoff ** attempt))
                time.sleep(sleep_s)
                continue
            else:
                res = resp
                cache[address] = res
                last_call_ts = time.time()
                break
        except Exception as e:
            if attempt >= max_attempts:
                res = {"status": "EXCEPTION", "error_message": str(e)}
                cache[address] = res
                last_call_ts = time.time()
                break
            time.sleep(min(60, (backoff ** attempt)))

    return {
        "lat": extract_lat_from_result(res),
        "lng": extract_lng_from_result(res),
        "formatted": extract_formatted_from_result(res),
        "status": res.get("status", ""),
        "last_call_ts": last_call_ts
    }


def extract_lat_from_result(res: Dict) -> str:
    if res.get("status") == "OK":
        results = res.get("results") or []
        if results:
            geometry = results[0].get("geometry", {})
            loc = geometry.get("location", {})
            return str(loc.get("lat", ""))
    return ""


def extract_lng_from_result(res: Dict) -> str:
    if res.get("status") == "OK":
        results = res.get("results") or []
        if results:
            geometry = results[0].get("geometry", {})
            loc = geometry.get("location", {})
            return str(loc.get("lng", ""))
    return ""


def extract_formatted_from_result(res: Dict) -> str:
    if res.get("status") == "OK":
        results = res.get("results") or []
        if results:
            return results[0].get("formatted_address", "")
    return ""


def process_rows(
    rows: Iterable[Dict[str, str]],
    fieldnames: List[str],
    address_col: str,
    city_col: Optional[str],
    district_col: Optional[str],
    api_key: str,
    country: str,
    language: str,
    region: str,
    qps: float,
    limit: Optional[int],
    cache_path: str,
) -> Iterable[Dict[str, str]]:
    # 载入缓存
    cache: Dict[str, Dict] = {}
    if os.path.exists(cache_path):
        try:
            with open(cache_path, "r", encoding="utf-8") as f:
                cache = json.load(f)
        except Exception:
            cache = {}

    last_call_ts = 0.0
    min_interval = 1.0 / max(qps, 0.1)

    cnt = 0
    for row in rows:
        cnt += 1
        if limit and cnt > limit:
            break

        parts = [row.get(address_col, "")]  # base address
        if city_col and row.get(city_col):
            parts.append(row[city_col])
        if district_col and row.get(district_col):
            parts.append(row[district_col])
        query = normalize_addr(
            ", ".join([p for p in parts if isinstance(p, str) and p.strip()])
        )

        if not query:
            row.update({
                "lat": "", "lng": "", "formatted_address": "",
                "partial_match": "", "place_id": "", "geocode_status": "NO_ADDRESS"
            })
            yield row
            continue

        if query in cache:
            res = cache[query]
        else:
            # 节流
            now = time.time()
            wait = last_call_ts + min_interval - now
            if wait > 0:
                time.sleep(wait)

            # 重试（指数退避）
            attempt, max_attempts, backoff = 0, 5, 1.5
            while True:
                attempt += 1
                try:
                    resp = geocode(query, api_key, country, language, region)
                    status = resp.get("status", "")
                    if status in ("OK", "ZERO_RESULTS"):
                        res = resp
                        cache[query] = res
                        last_call_ts = time.time()
                        break
                    elif status in ("OVER_QUERY_LIMIT",):
                        sleep_s = min(60, (backoff ** attempt))
                        time.sleep(sleep_s)
                        continue
                    else:
                        res = resp
                        cache[query] = res
                        last_call_ts = time.time()
                        break
                except Exception as e:
                    if attempt >= max_attempts:
                        res = {"status": "EXCEPTION", "error_message": str(e)}
                        cache[query] = res
                        break
                    time.sleep(min(60, (backoff ** attempt)))

            # 周期性写入缓存，防止中断丢失
            if cnt % 50 == 0:
                try:
                    with open(cache_path, "w", encoding="utf-8") as f:
                        json.dump(cache, f, ensure_ascii=False)
                except Exception:
                    pass

        status = res.get("status", "")
        lat = lng = formatted = place_id = ""
        partial_match = ""
        if status == "OK":
            results = res.get("results") or []
            result0 = results[0] if results else {}
            geometry = result0.get("geometry", {})
            loc = geometry.get("location", {})
            lat = loc.get("lat", "")
            lng = loc.get("lng", "")
            formatted = result0.get("formatted_address", "")
            place_id = result0.get("place_id", "")
            partial_match = str(result0.get("partial_match", False))
        elif status == "ZERO_RESULTS":
            pass
        else:
            pass

        row.update({
            "lat": lat, "lng": lng, "formatted_address": formatted,
            "partial_match": partial_match, "place_id": place_id,
            "geocode_status": status,
        })
        yield row

    # 结束保存缓存
    try:
        with open(cache_path, "w", encoding="utf-8") as f:
            json.dump(cache, f, ensure_ascii=False)
    except Exception:
        pass


def read_csv_rows(path: str) -> Tuple[Iterable[Dict[str, str]], List[str]]:
    fin = open(path, "r", encoding="utf-8-sig", newline="")
    reader = csv.DictReader(fin)
    fieldnames = list(reader.fieldnames or [])

    def row_iter():
        try:
            for r in reader:
                yield r
        finally:
            fin.close()

    return row_iter(), fieldnames


def write_csv_rows(path: str, fieldnames: List[str], rows: Iterable[Dict[str, str]]):
    with open(path, "w", encoding="utf-8", newline="") as fout:
        writer = csv.DictWriter(fout, fieldnames=fieldnames)
        writer.writeheader()
        for r in rows:
            writer.writerow(r)


def read_xlsx_rows(path: str, sheet: Optional[str]) -> Tuple[Iterable[Dict[str, str]], List[str]]:
    if not HAS_OPENPYXL:
        raise RuntimeError("未安装 openpyxl，无法直接读取 .xlsx。请安装 openpyxl 或导出为 CSV 再试。")
    wb = openpyxl.load_workbook(path)
    ws = wb[sheet] if sheet else wb.active

    rows_iter = ws.iter_rows(values_only=True)
    headers = next(rows_iter)
    header_list = [str(h) if h is not None else '' for h in headers]

    def gen():
        try:
            for values in rows_iter:
                d = {}
                for i, key in enumerate(header_list):
                    d[key] = values[i] if i < len(values) else None
                    if d[key] is None:
                        d[key] = ""
                    else:
                        d[key] = str(d[key])
                yield d
        finally:
            wb.close()

    return gen(), header_list


def write_xlsx_rows(src_path: str, out_path: str, fieldnames: List[str], rows: Iterable[Dict[str, str]], sheet: Optional[str]):
    if not HAS_OPENPYXL:
        raise RuntimeError("未安装 openpyxl，无法写出 .xlsx。请输出为 CSV。")

    # 读取原表，写到新文件（避免原地覆盖导致问题）
    wb_in = openpyxl.load_workbook(src_path)
    ws_in = wb_in[sheet] if sheet else wb_in.active

    wb_out = openpyxl.Workbook()
    ws_out = wb_out.active
    ws_out.title = ws_in.title

    # 写表头
    ws_out.append(fieldnames)

    # 写数据
    for r in rows:
        ws_out.append([r.get(c, "") for c in fieldnames])

    # 保存
    wb_out.save(out_path)
    wb_in.close()


def main():
    parser = argparse.ArgumentParser(description="Google Geocoding 批量地址转坐标")
    parser.add_argument("--input", required=True, help="输入文件路径：.csv 或 .xlsx")
    parser.add_argument("--sheet", default=None, help="Excel 工作表名（可选）")
    parser.add_argument("--output", default=None, help="输出文件路径（未指定则自动生成 *_geocoded.csv/xlsx）")

    # 单地址模式参数
    parser.add_argument("--address-col", default="地址", help="地址列名，默认：地址")
    parser.add_argument("--city-col", default=None, help="城市/县市列名（可选）")
    parser.add_argument("--district-col", default=None, help="区/乡镇列名（可选）")

    # 双地址模式参数
    parser.add_argument("--addr1-cols", default=None, help="地址1的列序号（逗号分隔，从1开始），例如：2,3,5")
    parser.add_argument("--addr2-col", type=int, default=None, help="地址2的列序号（从1开始），例如：6")
    parser.add_argument("--join-sep", default="", help="地址1各部分的连接符，默认无分隔符")

    parser.add_argument("--country", default="TW")
    parser.add_argument("--language", default="zh-TW")
    parser.add_argument("--region", default="tw")
    parser.add_argument("--qps", type=float, default=3.0, help="每秒请求上限，默认 3")
    parser.add_argument("--limit", type=int, default=None, help="仅处理前 N 行用于测试")
    parser.add_argument("--cache", default=".geocode_cache.json", help="缓存文件路径")
    parser.add_argument("--api-key", default="AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY", help="Google API Key")
    args = parser.parse_args()

    api_key = args.api_key or os.getenv("GOOGLE_MAPS_API_KEY")
    if not api_key:
        raise RuntimeError("请通过参数 --api-key 或环境变量 GOOGLE_MAPS_API_KEY 提供 Google API Key")

    # 判断是否为双地址模式
    dual_mode = bool(args.addr1_cols and args.addr2_col)

    in_lower = args.input.lower()
    is_xlsx = in_lower.endswith(".xlsx")
    is_csv = in_lower.endswith(".csv")

    if not (is_xlsx or is_csv):
        raise RuntimeError("仅支持 .xlsx 或 .csv 输入")

    # 读取
    if is_csv:
        row_iter, fieldnames = read_csv_rows(args.input)
    else:  # xlsx
        row_iter, fieldnames = read_xlsx_rows(args.input, args.sheet)

    # 验证参数
    if dual_mode:
        # 解析地址1列序号
        try:
            addr1_cols = [int(x.strip()) - 1 for x in args.addr1_cols.split(",")]  # 转为0-based索引
            addr2_col = args.addr2_col - 1  # 转为0-based索引
        except (ValueError, AttributeError):
            raise RuntimeError("--addr1-cols 格式错误，应为逗号分隔的数字，例如：2,3,5")

        if args.addr2_col is None:
            raise RuntimeError("双地址模式需要指定 --addr2-col")

        print(f"双地址模式：地址1由第{[x+1 for x in addr1_cols]}列组成，地址2为第{addr2_col+1}列")
    else:
        if args.address_col not in fieldnames:
            raise RuntimeError(f"找不到地址列：{args.address_col}，文件列名有：{fieldnames}")

    out_path = args.output
    if not out_path:
        suffix = "_dual_geocoded" if dual_mode else "_geocoded"
        if is_csv:
            out_path = args.input.rsplit(".", 1)[0] + suffix + ".csv"
        else:
            out_path = args.input.rsplit(".", 1)[0] + suffix + ".xlsx"

    out_fieldnames = ensure_extra_cols(list(fieldnames), dual_mode)

    # 处理
    if dual_mode:
        processed_rows = process_rows_dual(
            rows=row_iter,
            fieldnames=out_fieldnames,
            addr1_cols=addr1_cols,
            addr2_col=addr2_col,
            join_sep=args.join_sep,
            api_key=api_key,
            country=args.country,
            language=args.language,
            region=args.region,
            qps=args.qps,
            limit=args.limit,
            cache_path=args.cache,
        )
    else:
        processed_rows = process_rows(
            rows=row_iter,
            fieldnames=out_fieldnames,
            address_col=args.address_col,
            city_col=args.city_col,
            district_col=args.district_col,
            api_key=api_key,
            country=args.country,
            language=args.language,
            region=args.region,
            qps=args.qps,
            limit=args.limit,
            cache_path=args.cache,
        )

    # 写出
    if out_path.lower().endswith(".csv"):
        write_csv_rows(out_path, out_fieldnames, processed_rows)
    else:
        if not HAS_OPENPYXL:
            raise RuntimeError("输出为 .xlsx 需要 openpyxl，请改为输出 .csv 或安装 openpyxl。")
        write_xlsx_rows(args.input, out_path, out_fieldnames, processed_rows, args.sheet)

    print(f"完成：输出 {out_path}")


if __name__ == "__main__":
    main()

