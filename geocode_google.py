#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量用 Google Geocoding API 将地址转换为经纬度。
- 支持 CSV（标准库）
- 支持 Excel .xlsx（若系统已安装 openpyxl）
- 去重缓存、QPS 节流、指数退避重试

使用示例：
  设置环境变量：  set GOOGLE_MAPS_API_KEY=你的key
  先小样本：     python geocode_google.py --input 全部縣市加油站位置.xlsx --address-col 地址 --limit 5
  全量：         python geocode_google.py --input 全部縣市加油站位置.xlsx --address-col 地址
  若无 openpyxl：将 Excel 导出为 CSV 后：
                 python geocode_google.py --input 全部縣市加油站位置.csv --address-col 地址

输出会新增列：lat, lng, formatted_address, partial_match, place_id, geocode_status
"""

import os
import csv
import time
import json
import math
import argparse
import urllib.parse
import urllib.request
from typing import Dict, List, Iterable, Tuple, Optional

GOOGLE_API_URL = "https://maps.googleapis.com/maps/api/geocode/json"

try:
    import openpyxl  # type: ignore
    HAS_OPENPYXL = True
except Exception:
    HAS_OPENPYXL = False


def geocode(address: str, api_key: str, country: str = "TW", language: str = "zh-TW", region: str = "tw") -> Dict:
    params = {
        "address": address,
        "key": api_key,
        "language": language,
    }
    if country:
        params["components"] = f"country:{country}"
    if region:
        params["region"] = region
    url = f"{GOOGLE_API_URL}?{urllib.parse.urlencode(params)}"
    with urllib.request.urlopen(url, timeout=20) as resp:
        data = resp.read().decode("utf-8")
        return json.loads(data)


def normalize_addr(s: Optional[str]) -> str:
    if not s:
        return ""
    return s.strip()


def ensure_extra_cols(fieldnames: List[str]) -> List[str]:
    extra_cols = [
        "lat", "lng", "formatted_address", "partial_match", "place_id", "geocode_status",
    ]
    for c in extra_cols:
        if c not in fieldnames:
            fieldnames.append(c)
    return fieldnames


def process_rows(
    rows: Iterable[Dict[str, str]],
    fieldnames: List[str],
    address_col: str,
    city_col: Optional[str],
    district_col: Optional[str],
    api_key: str,
    country: str,
    language: str,
    region: str,
    qps: float,
    limit: Optional[int],
    cache_path: str,
) -> Iterable[Dict[str, str]]:
    # 载入缓存
    cache: Dict[str, Dict] = {}
    if os.path.exists(cache_path):
        try:
            with open(cache_path, "r", encoding="utf-8") as f:
                cache = json.load(f)
        except Exception:
            cache = {}

    last_call_ts = 0.0
    min_interval = 1.0 / max(qps, 0.1)

    cnt = 0
    for row in rows:
        cnt += 1
        if limit and cnt > limit:
            break

        parts = [row.get(address_col, "")]  # base address
        if city_col and row.get(city_col):
            parts.append(row[city_col])
        if district_col and row.get(district_col):
            parts.append(row[district_col])
        query = normalize_addr(
            ", ".join([p for p in parts if isinstance(p, str) and p.strip()])
        )

        if not query:
            row.update({
                "lat": "", "lng": "", "formatted_address": "",
                "partial_match": "", "place_id": "", "geocode_status": "NO_ADDRESS"
            })
            yield row
            continue

        if query in cache:
            res = cache[query]
        else:
            # 节流
            now = time.time()
            wait = last_call_ts + min_interval - now
            if wait > 0:
                time.sleep(wait)

            # 重试（指数退避）
            attempt, max_attempts, backoff = 0, 5, 1.5
            while True:
                attempt += 1
                try:
                    resp = geocode(query, api_key, country, language, region)
                    status = resp.get("status", "")
                    if status in ("OK", "ZERO_RESULTS"):
                        res = resp
                        cache[query] = res
                        last_call_ts = time.time()
                        break
                    elif status in ("OVER_QUERY_LIMIT",):
                        sleep_s = min(60, (backoff ** attempt))
                        time.sleep(sleep_s)
                        continue
                    else:
                        res = resp
                        cache[query] = res
                        last_call_ts = time.time()
                        break
                except Exception as e:
                    if attempt >= max_attempts:
                        res = {"status": "EXCEPTION", "error_message": str(e)}
                        cache[query] = res
                        break
                    time.sleep(min(60, (backoff ** attempt)))

            # 周期性写入缓存，防止中断丢失
            if cnt % 50 == 0:
                try:
                    with open(cache_path, "w", encoding="utf-8") as f:
                        json.dump(cache, f, ensure_ascii=False)
                except Exception:
                    pass

        status = res.get("status", "")
        lat = lng = formatted = place_id = ""
        partial_match = ""
        if status == "OK":
            results = res.get("results") or []
            result0 = results[0] if results else {}
            geometry = result0.get("geometry", {})
            loc = geometry.get("location", {})
            lat = loc.get("lat", "")
            lng = loc.get("lng", "")
            formatted = result0.get("formatted_address", "")
            place_id = result0.get("place_id", "")
            partial_match = str(result0.get("partial_match", False))
        elif status == "ZERO_RESULTS":
            pass
        else:
            pass

        row.update({
            "lat": lat, "lng": lng, "formatted_address": formatted,
            "partial_match": partial_match, "place_id": place_id,
            "geocode_status": status,
        })
        yield row

    # 结束保存缓存
    try:
        with open(cache_path, "w", encoding="utf-8") as f:
            json.dump(cache, f, ensure_ascii=False)
    except Exception:
        pass


def read_csv_rows(path: str) -> Tuple[Iterable[Dict[str, str]], List[str]]:
    fin = open(path, "r", encoding="utf-8-sig", newline="")
    reader = csv.DictReader(fin)
    fieldnames = list(reader.fieldnames or [])

    def row_iter():
        try:
            for r in reader:
                yield r
        finally:
            fin.close()

    return row_iter(), fieldnames


def write_csv_rows(path: str, fieldnames: List[str], rows: Iterable[Dict[str, str]]):
    with open(path, "w", encoding="utf-8", newline="") as fout:
        writer = csv.DictWriter(fout, fieldnames=fieldnames)
        writer.writeheader()
        for r in rows:
            writer.writerow(r)


def read_xlsx_rows(path: str, sheet: Optional[str]) -> Tuple[Iterable[Dict[str, str]], List[str]]:
    if not HAS_OPENPYXL:
        raise RuntimeError("未安装 openpyxl，无法直接读取 .xlsx。请安装 openpyxl 或导出为 CSV 再试。")
    wb = openpyxl.load_workbook(path)
    ws = wb[sheet] if sheet else wb.active

    rows_iter = ws.iter_rows(values_only=True)
    headers = next(rows_iter)
    header_list = [str(h) if h is not None else '' for h in headers]

    def gen():
        try:
            for values in rows_iter:
                d = {}
                for i, key in enumerate(header_list):
                    d[key] = values[i] if i < len(values) else None
                    if d[key] is None:
                        d[key] = ""
                    else:
                        d[key] = str(d[key])
                yield d
        finally:
            wb.close()

    return gen(), header_list


def write_xlsx_rows(src_path: str, out_path: str, fieldnames: List[str], rows: Iterable[Dict[str, str]], sheet: Optional[str]):
    if not HAS_OPENPYXL:
        raise RuntimeError("未安装 openpyxl，无法写出 .xlsx。请输出为 CSV。")

    # 读取原表，写到新文件（避免原地覆盖导致问题）
    wb_in = openpyxl.load_workbook(src_path)
    ws_in = wb_in[sheet] if sheet else wb_in.active

    wb_out = openpyxl.Workbook()
    ws_out = wb_out.active
    ws_out.title = ws_in.title

    # 写表头
    ws_out.append(fieldnames)

    # 写数据
    for r in rows:
        ws_out.append([r.get(c, "") for c in fieldnames])

    # 保存
    wb_out.save(out_path)
    wb_in.close()


def main():
    parser = argparse.ArgumentParser(description="Google Geocoding 批量地址转坐标")
    parser.add_argument("--input", required=True, help="输入文件路径：.csv 或 .xlsx")
    parser.add_argument("--sheet", default=None, help="Excel 工作表名（可选）")
    parser.add_argument("--output", default=None, help="输出文件路径（未指定则自动生成 *_geocoded.csv/xlsx）")
    parser.add_argument("--address-col", default="地址", help="地址列名，默认：地址")
    parser.add_argument("--city-col", default=None, help="城市/县市列名（可选）")
    parser.add_argument("--district-col", default=None, help="区/乡镇列名（可选）")
    parser.add_argument("--country", default="TW")
    parser.add_argument("--language", default="zh-TW")
    parser.add_argument("--region", default="tw")
    parser.add_argument("--qps", type=float, default=3.0, help="每秒请求上限，默认 3")
    parser.add_argument("--limit", type=int, default=None, help="仅处理前 N 行用于测试")
    parser.add_argument("--cache", default=".geocode_cache.json", help="缓存文件路径")
    parser.add_argument("--api-key", default=None, help="可直接传入 API Key（否则从环境变量 GOOGLE_MAPS_API_KEY 读取）")
    args = parser.parse_args()

    api_key = args.api_key or os.getenv("GOOGLE_MAPS_API_KEY")
    if not api_key:
        raise RuntimeError("请通过参数 --api-key 或环境变量 GOOGLE_MAPS_API_KEY 提供 Google API Key")

    in_lower = args.input.lower()
    is_xlsx = in_lower.endswith(".xlsx")
    is_csv = in_lower.endswith(".csv")

    if not (is_xlsx or is_csv):
        raise RuntimeError("仅支持 .xlsx 或 .csv 输入")

    # 读取
    if is_csv:
        row_iter, fieldnames = read_csv_rows(args.input)
    else:  # xlsx
        row_iter, fieldnames = read_xlsx_rows(args.input, args.sheet)

    if args.address_col not in fieldnames:
        raise RuntimeError(f"找不到地址列：{args.address_col}，文件列名有：{fieldnames}")

    out_path = args.output
    if not out_path:
        if is_csv:
            out_path = args.input.rsplit(".", 1)[0] + "_geocoded.csv"
        else:
            out_path = args.input.rsplit(".", 1)[0] + "_geocoded.xlsx"

    out_fieldnames = ensure_extra_cols(list(fieldnames))

    # 处理
    processed_rows = process_rows(
        rows=row_iter,
        fieldnames=out_fieldnames,
        address_col=args.address_col,
        city_col=args.city_col,
        district_col=args.district_col,
        api_key=api_key,
        country=args.country,
        language=args.language,
        region=args.region,
        qps=args.qps,
        limit=args.limit,
        cache_path=args.cache,
    )

    # 写出
    if out_path.lower().endswith(".csv"):
        write_csv_rows(out_path, out_fieldnames, processed_rows)
    else:
        if not HAS_OPENPYXL:
            raise RuntimeError("输出为 .xlsx 需要 openpyxl，请改为输出 .csv 或安装 openpyxl。")
        write_xlsx_rows(args.input, out_path, out_fieldnames, processed_rows, args.sheet)

    print(f"完成：输出 {out_path}")


if __name__ == "__main__":
    main()

